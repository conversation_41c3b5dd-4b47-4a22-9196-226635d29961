#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { program } = require('commander');

// Command line argument parsing
program
  .name('gh')
  .description('Fetch GitHub sponsors data and update CSV file')
  .option('--output <file>', 'Output CSV file path', 'data/github-sponsors.csv')
  .option('--dry-run', 'Show what would be written without updating file')
  .option('--verbose', 'Show detailed output')
  .addHelpText('after', `
Examples:
  Refresh GitHub sponsors data:
    $ GITHUB_API_KEY=your_token node scripts/gh.js

  Dry run to see what would be updated:
    $ GITHUB_API_KEY=your_token node scripts/gh.js --dry-run

Environment Variables:
  GITHUB_API_KEY - Required GitHub Personal Access Token with 'user:read' scope
`)
  .parse();

const options = program.opts();

// Check for required environment variables
const checkEnvironment = () => {
  if (!process.env.GITHUB_API_KEY) {
    console.error('❌ Missing required environment variable: GITHUB_API_KEY');
    console.error('💡 Get a token from: https://github.com/settings/tokens');
    console.error('💡 Required scopes: user:read');
    console.error('💡 Usage: GITHUB_API_KEY=your_token node scripts/gh.js');
    process.exit(1);
  }
};

// Make GitHub GraphQL API request
const githubGraphQLRequest = async (query, variables = {}) => {
  const url = 'https://api.github.com/graphql';

  if (options.verbose) {
    console.log(`🔗 Fetching GraphQL: ${url}`);
    console.log(`📝 Query: ${query.substring(0, 100)}...`);
  }

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.GITHUB_API_KEY}`,
        'Content-Type': 'application/json',
        'User-Agent': 'solnic-dev-sponsors-script'
      },
      body: JSON.stringify({
        query,
        variables
      })
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('GitHub API authentication failed. Check your GITHUB_API_KEY.');
      } else if (response.status === 403) {
        throw new Error('GitHub API rate limit exceeded or insufficient permissions.');
      } else {
        throw new Error(`GitHub API error: ${response.status} ${response.statusText}`);
      }
    }

    const data = await response.json();

    if (data.errors) {
      throw new Error(`GraphQL errors: ${data.errors.map(e => e.message).join(', ')}`);
    }

    return data.data;
  } catch (error) {
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      console.error('❌ Network error. Make sure you have internet connection.');
    }
    throw error;
  }
};

// Get all sponsors using GraphQL API
const getAllSponsors = async () => {
  console.log('📊 Fetching GitHub sponsors data...');

  const query = `
    query($cursor: String) {
      viewer {
        sponsorshipsAsMaintainer(first: 100, after: $cursor, includePrivate: true) {
          pageInfo {
            hasNextPage
            endCursor
          }
          nodes {
            sponsor {
              login
              name
              avatarUrl
              id
            }
            tier {
              name
              monthlyPriceInDollars
            }
            createdAt
            isActive
          }
        }
      }
    }
  `;

  let allSponsors = [];
  let hasNextPage = true;
  let cursor = null;

  while (hasNextPage) {
    if (options.verbose) {
      console.log(`📄 Fetching sponsors page...`);
    }

    const data = await githubGraphQLRequest(query, { cursor });
    const sponsorships = data.viewer.sponsorshipsAsMaintainer;

    allSponsors = allSponsors.concat(sponsorships.nodes);

    hasNextPage = sponsorships.pageInfo.hasNextPage;
    cursor = sponsorships.pageInfo.endCursor;
  }

  console.log(`✅ Found ${allSponsors.length} total sponsorships`);
  return allSponsors;
};

// Format sponsor data for CSV
const formatSponsorData = (sponsorships) => {
  return sponsorships.map(sponsorship => {
    const sponsor = sponsorship.sponsor;
    const tier = sponsorship.tier;

    return {
      sponsor_handle: sponsor.login,
      sponsor_name: sponsor.name || sponsor.login,
      avatar_url: sponsor.avatarUrl,
      is_active: sponsorship.isActive ? 'true' : 'false',
      sponsorship_started_on: sponsorship.createdAt.split('T')[0], // Extract date part
      tier_name: tier ? tier.name : '',
      tier_monthly_price_in_dollars: tier ? tier.monthlyPriceInDollars.toString() : ''
    };
  });
};

// Convert data to CSV format
const generateCSV = (data) => {
  const headers = [
    'sponsor_handle',
    'sponsor_name',
    'avatar_url',
    'is_active',
    'sponsorship_started_on',
    'tier_name',
    'tier_monthly_price_in_dollars'
  ];

  const csvLines = [headers.join(',')];

  data.forEach(row => {
    const csvRow = headers.map(header => {
      const value = row[header] || '';
      // Escape commas and quotes in CSV values
      if (value.includes(',') || value.includes('"') || value.includes('\n')) {
        return `"${value.replace(/"/g, '""')}"`;
      }
      return value;
    });
    csvLines.push(csvRow.join(','));
  });

  return csvLines.join('\n');
};

// Merge with existing data to preserve historical information
const mergeWithExistingData = (newData, existingCsvPath) => {
  if (!fs.existsSync(existingCsvPath)) {
    console.log('📄 No existing CSV file found, creating new one');
    return newData;
  }

  try {
    console.log('📄 Reading existing CSV file...');
    const existingContent = fs.readFileSync(existingCsvPath, 'utf8');
    const lines = existingContent.trim().split('\n');
    const headers = lines[0].split(',');

    const existingData = lines.slice(1).map(line => {
      const values = line.split(',');
      const row = {};
      headers.forEach((header, index) => {
        row[header] = values[index] || '';
      });
      return row;
    });

    // Create a map of existing sponsors by handle
    const existingMap = new Map();
    existingData.forEach(sponsor => {
      existingMap.set(sponsor.sponsor_handle, sponsor);
    });

    // Merge new data with existing, preserving historical info
    const mergedData = newData.map(newSponsor => {
      const existing = existingMap.get(newSponsor.sponsor_handle);
      if (existing) {
        // Preserve historical data but update current status
        return {
          ...existing,
          sponsor_name: newSponsor.sponsor_name, // Update name in case it changed
          avatar_url: newSponsor.avatar_url, // Update avatar in case it changed
          is_active: newSponsor.is_active // Update active status
        };
      }
      return newSponsor;
    });

    // Add any sponsors from existing data that aren't in the new data (they might be past sponsors)
    existingData.forEach(existing => {
      if (!newData.find(n => n.sponsor_handle === existing.sponsor_handle)) {
        mergedData.push({
          ...existing,
          is_active: 'false' // Mark as inactive since they're not in current data
        });
      }
    });

    console.log(`✅ Merged data: ${mergedData.length} total sponsors`);
    return mergedData;
  } catch (error) {
    console.warn('⚠️  Error reading existing CSV, creating new file:', error.message);
    return newData;
  }
};

// Main function
const main = async () => {
  try {
    console.log('🚀 Starting GitHub sponsors data refresh...');

    // Check environment
    checkEnvironment();

    // Fetch data from GitHub GraphQL API
    const sponsorships = await getAllSponsors();

    // Format the data
    const formattedData = formatSponsorData(sponsorships);

    // Merge with existing data
    const outputPath = path.resolve(options.output);
    const mergedData = mergeWithExistingData(formattedData, outputPath);

    // Generate CSV
    const csvContent = generateCSV(mergedData);

    // Count active vs inactive
    const activeCount = mergedData.filter(s => s.is_active === 'true').length;
    const inactiveCount = mergedData.filter(s => s.is_active === 'false').length;

    if (options.dryRun) {
      console.log('\n🔍 Dry run - would write CSV with:');
      console.log(`📊 Total sponsors: ${mergedData.length}`);
      console.log(`📊 Active sponsors: ${activeCount}`);
      console.log(`📊 Past sponsors: ${inactiveCount}`);
      console.log(`📄 Output file: ${outputPath}`);
      console.log('\n🔍 CSV preview (first 5 lines):');
      console.log(csvContent.split('\n').slice(0, 5).join('\n'));
      console.log('\n🔍 Dry run completed - no file was written');
      return;
    }

    // Write the file
    fs.writeFileSync(outputPath, csvContent);

    console.log('✅ GitHub sponsors CSV updated successfully!');
    console.log(`📊 Total sponsors: ${mergedData.length}`);
    console.log(`📊 Active sponsors: ${activeCount}`);
    console.log(`📊 Past sponsors: ${inactiveCount}`);
    console.log(`💾 Output saved to: ${outputPath}`);

    if (options.verbose) {
      console.log('\n📋 Next steps:');
      console.log('1. Review the updated CSV file');
      console.log('2. Run: node scripts/ghost.js --page github-sponsors');
      console.log('3. Your sponsors page will be automatically updated!');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);

    if (options.verbose) {
      console.error('Stack trace:', error.stack);
    }

    process.exit(1);
  }
};

// Run the script
if (require.main === module) {
  main();
}
