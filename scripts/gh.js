#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { program } = require('commander');
const GitHub = require('github-api');

// Command line argument parsing
program
  .name('gh')
  .description('Fetch GitHub sponsors data and update CSV file')
  .option('--output <file>', 'Output CSV file path', 'data/github-sponsors.csv')
  .option('--dry-run', 'Show what would be written without updating file')
  .option('--verbose', 'Show detailed output')
  .addHelpText('after', `
Examples:
  Refresh GitHub sponsors data:
    $ GITHUB_API_KEY=your_token node scripts/gh.js

  Dry run to see what would be updated:
    $ GITHUB_API_KEY=your_token node scripts/gh.js --dry-run

Environment Variables:
  GITHUB_API_KEY - Required GitHub Personal Access Token with 'user:read' scope

Note: Due to GitHub API limitations, this script will preserve existing sponsors data
and only update what's accessible through the REST API.
`)
  .parse();

const options = program.opts();

// Check for required environment variables
const checkEnvironment = () => {
  if (!process.env.GITHUB_API_KEY) {
    console.error('❌ Missing required environment variable: GITHUB_API_KEY');
    console.error('💡 Get a token from: https://github.com/settings/tokens');
    console.error('💡 Required scopes: user:read');
    console.error('💡 Usage: GITHUB_API_KEY=your_token node scripts/gh.js');
    process.exit(1);
  }
};

// Initialize GitHub API client
const initializeGitHubClient = () => {
  try {
    const gh = new GitHub({
      token: process.env.GITHUB_API_KEY
    });

    if (options.verbose) {
      console.log('✅ GitHub API client initialized');
    }

    return gh;
  } catch (error) {
    console.error('❌ Failed to initialize GitHub API client:', error.message);
    process.exit(1);
  }
};

// Get user information and validate API access
const validateAPIAccess = async (gh) => {
  try {
    console.log('📊 Validating GitHub API access...');

    const user = gh.getUser();
    const { data: userInfo } = await user.getProfile();

    if (options.verbose) {
      console.log(`✅ Authenticated as: ${userInfo.login} (${userInfo.name || 'No name'})`);
    }

    return userInfo;
  } catch (error) {
    if (error.response && error.response.status === 401) {
      throw new Error('GitHub API authentication failed. Check your GITHUB_API_KEY.');
    } else if (error.response && error.response.status === 403) {
      throw new Error('GitHub API rate limit exceeded or insufficient permissions.');
    } else {
      throw new Error(`GitHub API error: ${error.message}`);
    }
  }
};

// Note: GitHub Sponsors data is not available through REST API
// This function preserves existing data and provides a warning
const getSponsorsData = async (gh) => {
  console.log('⚠️  GitHub Sponsors data is not accessible via REST API');
  console.log('📄 The script will preserve existing sponsors data from CSV file');

  // Validate that we can access the API
  await validateAPIAccess(gh);

  // Return empty array since we can't fetch new sponsors data
  // The existing data will be preserved in the merge function
  return [];
};

// Since we can't fetch new sponsors data, this function returns empty array
// The existing data will be preserved through the merge function
const formatSponsorData = (sponsorships) => {
  // sponsorships will be empty array since REST API doesn't provide sponsors data
  return sponsorships.map(sponsorship => {
    const sponsor = sponsorship.sponsor;
    const tier = sponsorship.tier;

    return {
      sponsor_handle: sponsor.login,
      sponsor_name: sponsor.name || sponsor.login,
      avatar_url: sponsor.avatarUrl,
      is_active: sponsorship.isActive ? 'true' : 'false',
      sponsorship_started_on: sponsorship.createdAt.split('T')[0], // Extract date part
      tier_name: tier ? tier.name : '',
      tier_monthly_price_in_dollars: tier ? tier.monthlyPriceInDollars.toString() : ''
    };
  });
};

// Convert data to CSV format
const generateCSV = (data) => {
  const headers = [
    'sponsor_handle',
    'sponsor_name',
    'avatar_url',
    'is_active',
    'sponsorship_started_on',
    'tier_name',
    'tier_monthly_price_in_dollars'
  ];

  const csvLines = [headers.join(',')];

  data.forEach(row => {
    const csvRow = headers.map(header => {
      const value = row[header] || '';
      // Escape commas and quotes in CSV values
      if (value.includes(',') || value.includes('"') || value.includes('\n')) {
        return `"${value.replace(/"/g, '""')}"`;
      }
      return value;
    });
    csvLines.push(csvRow.join(','));
  });

  return csvLines.join('\n');
};

// Preserve existing data since we can't fetch new sponsors data via REST API
const mergeWithExistingData = (newData, existingCsvPath) => {
  if (!fs.existsSync(existingCsvPath)) {
    console.log('📄 No existing CSV file found');
    console.log('⚠️  Cannot create new sponsors data - GitHub Sponsors API requires GraphQL');
    console.log('💡 Please manually create the CSV file or use GitHub CLI with proper permissions');
    return [];
  }

  try {
    console.log('📄 Reading existing CSV file...');
    const existingContent = fs.readFileSync(existingCsvPath, 'utf8');
    const lines = existingContent.trim().split('\n');

    if (lines.length < 2) {
      console.log('⚠️  CSV file exists but appears to be empty');
      return [];
    }

    const headers = lines[0].split(',');

    const existingData = lines.slice(1).map(line => {
      const values = line.split(',');
      const row = {};
      headers.forEach((header, index) => {
        row[header] = values[index] || '';
      });
      return row;
    });

    // Since we can't fetch new data, we just preserve existing data
    // In the future, when GraphQL access is available, this can be enhanced
    console.log(`✅ Preserved existing data: ${existingData.length} total sponsors`);
    console.log('💡 To update sponsors data, you need GraphQL API access or manual CSV updates');

    return existingData;
  } catch (error) {
    console.warn('⚠️  Error reading existing CSV:', error.message);
    return [];
  }
};

// Main function
const main = async () => {
  try {
    console.log('🚀 Starting GitHub sponsors data refresh...');

    // Check environment
    checkEnvironment();

    // Initialize GitHub API client
    const gh = initializeGitHubClient();

    // Attempt to get sponsors data (will return empty array due to API limitations)
    const sponsorships = await getSponsorsData(gh);

    // Format the data (will be empty)
    const formattedData = formatSponsorData(sponsorships);

    // Preserve existing data since we can't fetch new data
    const outputPath = path.resolve(options.output);
    const mergedData = mergeWithExistingData(formattedData, outputPath);

    if (mergedData.length === 0) {
      console.log('⚠️  No sponsors data available to process');
      console.log('💡 To update sponsors data, you need:');
      console.log('   1. GraphQL API access with proper permissions, or');
      console.log('   2. Manual CSV file updates');
      return;
    }

    // Generate CSV
    const csvContent = generateCSV(mergedData);

    // Count active vs inactive
    const activeCount = mergedData.filter(s => s.is_active === 'true').length;
    const inactiveCount = mergedData.filter(s => s.is_active === 'false').length;

    if (options.dryRun) {
      console.log('\n🔍 Dry run - would write CSV with:');
      console.log(`📊 Total sponsors: ${mergedData.length}`);
      console.log(`📊 Active sponsors: ${activeCount}`);
      console.log(`📊 Past sponsors: ${inactiveCount}`);
      console.log(`📄 Output file: ${outputPath}`);
      console.log('\n🔍 CSV preview (first 5 lines):');
      console.log(csvContent.split('\n').slice(0, 5).join('\n'));
      console.log('\n🔍 Dry run completed - no file was written');
      return;
    }

    // Write the file
    fs.writeFileSync(outputPath, csvContent);

    console.log('✅ GitHub sponsors CSV preserved successfully!');
    console.log(`📊 Total sponsors: ${mergedData.length}`);
    console.log(`📊 Active sponsors: ${activeCount}`);
    console.log(`📊 Past sponsors: ${inactiveCount}`);
    console.log(`💾 Output saved to: ${outputPath}`);

    if (options.verbose) {
      console.log('\n📋 Next steps:');
      console.log('1. Review the preserved CSV file');
      console.log('2. Run: node scripts/ghost.js --page github-sponsors');
      console.log('3. Your sponsors page will use the existing data');
      console.log('\n💡 To get fresh sponsors data:');
      console.log('   - Use GitHub CLI: gh api graphql -f query="..." (requires proper permissions)');
      console.log('   - Or manually update the CSV file');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);

    if (options.verbose) {
      console.error('Stack trace:', error.stack);
    }

    process.exit(1);
  }
};

// Run the script
if (require.main === module) {
  main();
}
