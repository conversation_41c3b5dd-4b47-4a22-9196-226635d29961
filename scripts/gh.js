#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { program } = require('commander');
const GitHub = require('github-api');
const { createAppAuth } = require('@octokit/auth-app');
const { Octokit } = require('@octokit/rest');

// Command line argument parsing
program
  .name('gh')
  .description('Fetch GitHub sponsors data and update CSV file')
  .option('--output <file>', 'Output CSV file path', 'data/github-sponsors.csv')
  .option('--dry-run', 'Show what would be written without updating file')
  .option('--verbose', 'Show detailed output')
  .addHelpText('after', `
Examples:
  Refresh GitHub sponsors data:
    $ GITHUB_API_KEY=your_token node scripts/gh.js

  Dry run to see what would be updated:
    $ GITHUB_API_KEY=your_token node scripts/gh.js --dry-run

Environment Variables:
  GITHUB_API_KEY - GitHub Personal Access Token (limited functionality)

  OR for full sponsors access:

  GITHUB_APP_ID - GitHub App ID
  GITHUB_APP_PRIVATE_KEY_PATH - Path to GitHub App private key (.pem file)
  GITHUB_APP_INSTALLATION_ID - Installation ID (optional, will auto-detect)

Note: Personal Access Tokens cannot access sponsors data. Use a GitHub App for full functionality.
`)
  .parse();

const options = program.opts();

// Check for required environment variables
const checkEnvironment = () => {
  const hasPersonalToken = !!process.env.GITHUB_API_KEY;
  const hasAppCredentials = !!(process.env.GITHUB_APP_ID && process.env.GITHUB_APP_PRIVATE_KEY_PATH);

  if (!hasPersonalToken && !hasAppCredentials) {
    console.error('❌ Missing required environment variables');
    console.error('');
    console.error('Option 1 - Personal Access Token (limited functionality):');
    console.error('  GITHUB_API_KEY=your_token');
    console.error('');
    console.error('Option 2 - GitHub App (full sponsors access):');
    console.error('  GITHUB_APP_ID=your_app_id');
    console.error('  GITHUB_APP_PRIVATE_KEY_PATH=/path/to/private-key.pem');
    console.error('  GITHUB_APP_INSTALLATION_ID=installation_id (optional)');
    console.error('');
    console.error('💡 For sponsors data access, you need a GitHub App with sponsors permissions');
    process.exit(1);
  }

  return { hasPersonalToken, hasAppCredentials };
};

// Initialize GitHub API client
const initializeGitHubClient = async (authConfig) => {
  try {
    if (authConfig.hasAppCredentials) {
      // Use GitHub App authentication for full sponsors access
      const privateKey = fs.readFileSync(process.env.GITHUB_APP_PRIVATE_KEY_PATH, 'utf8');

      const auth = createAppAuth({
        appId: process.env.GITHUB_APP_ID,
        privateKey: privateKey,
        installationId: process.env.GITHUB_APP_INSTALLATION_ID
      });

      const octokit = new Octokit({
        auth: auth
      });

      if (options.verbose) {
        console.log('✅ GitHub App client initialized with sponsors access');
      }

      return { client: octokit, type: 'app' };
    } else {
      // Use personal access token (limited functionality)
      const gh = new GitHub({
        token: process.env.GITHUB_API_KEY
      });

      if (options.verbose) {
        console.log('✅ GitHub API client initialized (personal token - limited functionality)');
      }

      return { client: gh, type: 'personal' };
    }
  } catch (error) {
    console.error('❌ Failed to initialize GitHub API client:', error.message);
    if (error.code === 'ENOENT') {
      console.error('💡 Make sure the private key file path is correct');
    }
    process.exit(1);
  }
};

// Get user information and validate API access
const validateAPIAccess = async (gh) => {
  try {
    console.log('📊 Validating GitHub API access...');

    const user = gh.getUser();
    const { data: userInfo } = await user.getProfile();

    if (options.verbose) {
      console.log(`✅ Authenticated as: ${userInfo.login} (${userInfo.name || 'No name'})`);
    }

    return userInfo;
  } catch (error) {
    if (error.response && error.response.status === 401) {
      throw new Error('GitHub API authentication failed. Check your GITHUB_API_KEY.');
    } else if (error.response && error.response.status === 403) {
      throw new Error('GitHub API rate limit exceeded or insufficient permissions.');
    } else {
      throw new Error(`GitHub API error: ${error.message}`);
    }
  }
};

// Fetch sponsors data using GraphQL (requires GitHub App with sponsors permissions)
const fetchSponsorsWithGraphQL = async (octokit) => {
  console.log('📊 Fetching GitHub sponsors data via GraphQL...');

  const query = `
    query($cursor: String) {
      viewer {
        sponsorshipsAsMaintainer(first: 100, after: $cursor, includePrivate: true) {
          pageInfo {
            hasNextPage
            endCursor
          }
          nodes {
            sponsor {
              login
              name
              avatarUrl
              id
            }
            tier {
              name
              monthlyPriceInDollars
            }
            createdAt
            isActive
          }
        }
      }
    }
  `;

  let allSponsors = [];
  let hasNextPage = true;
  let cursor = null;

  while (hasNextPage) {
    if (options.verbose) {
      console.log(`📄 Fetching sponsors page...`);
    }

    const response = await octokit.graphql(query, { cursor });
    const sponsorships = response.viewer.sponsorshipsAsMaintainer;

    allSponsors = allSponsors.concat(sponsorships.nodes);

    hasNextPage = sponsorships.pageInfo.hasNextPage;
    cursor = sponsorships.pageInfo.endCursor;
  }

  console.log(`✅ Found ${allSponsors.length} total sponsorships`);
  return allSponsors;
};

// Get sponsors data based on authentication type
const getSponsorsData = async (githubClient) => {
  if (githubClient.type === 'app') {
    try {
      return await fetchSponsorsWithGraphQL(githubClient.client);
    } catch (error) {
      console.error('❌ Failed to fetch sponsors data:', error.message);
      console.log('📄 Falling back to preserving existing data');
      return [];
    }
  } else {
    console.log('⚠️  Personal Access Tokens cannot access GitHub Sponsors data');
    console.log('📄 The script will preserve existing sponsors data from CSV file');

    // Validate that we can access the API
    await validateAPIAccess(githubClient.client);

    return [];
  }
};

// Since we can't fetch new sponsors data, this function returns empty array
// The existing data will be preserved through the merge function
const formatSponsorData = (sponsorships) => {
  // sponsorships will be empty array since REST API doesn't provide sponsors data
  return sponsorships.map(sponsorship => {
    const sponsor = sponsorship.sponsor;
    const tier = sponsorship.tier;

    return {
      sponsor_handle: sponsor.login,
      sponsor_name: sponsor.name || sponsor.login,
      avatar_url: sponsor.avatarUrl,
      is_active: sponsorship.isActive ? 'true' : 'false',
      sponsorship_started_on: sponsorship.createdAt.split('T')[0], // Extract date part
      tier_name: tier ? tier.name : '',
      tier_monthly_price_in_dollars: tier ? tier.monthlyPriceInDollars.toString() : ''
    };
  });
};

// Convert data to CSV format
const generateCSV = (data) => {
  const headers = [
    'sponsor_handle',
    'sponsor_name',
    'avatar_url',
    'is_active',
    'sponsorship_started_on',
    'tier_name',
    'tier_monthly_price_in_dollars'
  ];

  const csvLines = [headers.join(',')];

  data.forEach(row => {
    const csvRow = headers.map(header => {
      const value = row[header] || '';
      // Escape commas and quotes in CSV values
      if (value.includes(',') || value.includes('"') || value.includes('\n')) {
        return `"${value.replace(/"/g, '""')}"`;
      }
      return value;
    });
    csvLines.push(csvRow.join(','));
  });

  return csvLines.join('\n');
};

// Merge new data with existing data, or preserve existing if no new data available
const mergeWithExistingData = (newData, existingCsvPath) => {
  const hasNewData = newData && newData.length > 0;

  if (!fs.existsSync(existingCsvPath)) {
    if (hasNewData) {
      console.log('📄 No existing CSV file found, creating new one with fresh data');
      return newData;
    } else {
      console.log('📄 No existing CSV file found and no new data available');
      console.log('⚠️  Cannot create sponsors data - need GitHub App with sponsors permissions');
      return [];
    }
  }

  try {
    console.log('📄 Reading existing CSV file...');
    const existingContent = fs.readFileSync(existingCsvPath, 'utf8');
    const lines = existingContent.trim().split('\n');

    if (lines.length < 2) {
      if (hasNewData) {
        console.log('⚠️  CSV file exists but appears to be empty, using new data');
        return newData;
      } else {
        console.log('⚠️  CSV file exists but appears to be empty');
        return [];
      }
    }

    const headers = lines[0].split(',');
    const existingData = lines.slice(1).map(line => {
      const values = line.split(',');
      const row = {};
      headers.forEach((header, index) => {
        row[header] = values[index] || '';
      });
      return row;
    });

    if (!hasNewData) {
      // No new data available, preserve existing
      console.log(`✅ Preserved existing data: ${existingData.length} total sponsors`);
      console.log('💡 To get fresh data, use a GitHub App with sponsors permissions');
      return existingData;
    }

    // Merge new data with existing data
    console.log('📊 Merging new sponsors data with existing data...');

    const existingMap = new Map();
    existingData.forEach(sponsor => {
      existingMap.set(sponsor.sponsor_handle, sponsor);
    });

    // Update existing sponsors with new data
    const mergedData = newData.map(newSponsor => {
      const existing = existingMap.get(newSponsor.sponsor_handle);
      if (existing) {
        return {
          ...existing,
          sponsor_name: newSponsor.sponsor_name,
          avatar_url: newSponsor.avatar_url,
          is_active: newSponsor.is_active,
          tier_name: newSponsor.tier_name,
          tier_monthly_price_in_dollars: newSponsor.tier_monthly_price_in_dollars
        };
      }
      return newSponsor;
    });

    // Add past sponsors not in new data
    existingData.forEach(existing => {
      if (!newData.find(n => n.sponsor_handle === existing.sponsor_handle)) {
        mergedData.push({
          ...existing,
          is_active: 'false'
        });
      }
    });

    console.log(`✅ Merged data: ${mergedData.length} total sponsors (${newData.length} fresh, ${mergedData.length - newData.length} historical)`);
    return mergedData;

  } catch (error) {
    console.warn('⚠️  Error reading existing CSV:', error.message);
    return hasNewData ? newData : [];
  }
};

// Main function
const main = async () => {
  try {
    console.log('🚀 Starting GitHub sponsors data refresh...');

    // Check environment
    const authConfig = checkEnvironment();

    // Initialize GitHub API client
    const githubClient = await initializeGitHubClient(authConfig);

    // Attempt to get sponsors data
    const sponsorships = await getSponsorsData(githubClient);

    // Format the data
    const formattedData = formatSponsorData(sponsorships);

    // Merge new data with existing data
    const outputPath = path.resolve(options.output);
    const mergedData = mergeWithExistingData(formattedData, outputPath);

    if (mergedData.length === 0) {
      console.log('⚠️  No sponsors data available to process');
      console.log('💡 To get sponsors data, you need:');
      console.log('   1. A GitHub App with sponsors permissions, or');
      console.log('   2. Manual CSV file creation');
      console.log('');
      console.log('📖 See the updated help for GitHub App setup instructions');
      return;
    }

    // Generate CSV
    const csvContent = generateCSV(mergedData);

    // Count active vs inactive
    const activeCount = mergedData.filter(s => s.is_active === 'true').length;
    const inactiveCount = mergedData.filter(s => s.is_active === 'false').length;

    if (options.dryRun) {
      console.log('\n🔍 Dry run - would write CSV with:');
      console.log(`📊 Total sponsors: ${mergedData.length}`);
      console.log(`📊 Active sponsors: ${activeCount}`);
      console.log(`📊 Past sponsors: ${inactiveCount}`);
      console.log(`📄 Output file: ${outputPath}`);
      console.log('\n🔍 CSV preview (first 5 lines):');
      console.log(csvContent.split('\n').slice(0, 5).join('\n'));
      console.log('\n🔍 Dry run completed - no file was written');
      return;
    }

    // Write the file
    fs.writeFileSync(outputPath, csvContent);

    console.log('✅ GitHub sponsors CSV preserved successfully!');
    console.log(`📊 Total sponsors: ${mergedData.length}`);
    console.log(`📊 Active sponsors: ${activeCount}`);
    console.log(`📊 Past sponsors: ${inactiveCount}`);
    console.log(`💾 Output saved to: ${outputPath}`);

    if (options.verbose) {
      console.log('\n📋 Next steps:');
      console.log('1. Review the preserved CSV file');
      console.log('2. Run: node scripts/ghost.js --page github-sponsors');
      console.log('3. Your sponsors page will use the existing data');
      console.log('\n💡 To get fresh sponsors data:');
      console.log('   - Use GitHub CLI: gh api graphql -f query="..." (requires proper permissions)');
      console.log('   - Or manually update the CSV file');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);

    if (options.verbose) {
      console.error('Stack trace:', error.stack);
    }

    process.exit(1);
  }
};

// Run the script
if (require.main === module) {
  main();
}
